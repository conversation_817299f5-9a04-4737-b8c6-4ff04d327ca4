# Import services from domain-specific modules
from .authorization.services import (
    GroupService, AuditService, SecurityService, PermissionService
)

# Re-export for backward compatibility
__all__ = [
    'GroupService', 'AuditService', 'SecurityService', 'PermissionService'
]

# All RBAC-related services have been moved to apps.staff.authorization.services
# This file now serves as a compatibility layer for imports
