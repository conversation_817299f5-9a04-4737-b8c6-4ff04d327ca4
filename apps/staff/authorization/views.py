from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.db import transaction
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedel<PERSON>

from .models import GroupMembership, PermissionAudit
from .serializers import (
    GroupSerializer, UserBasicSerializer, UserDetailSerializer,
    GroupMembershipSerializer, UserGroupAssignmentSerializer,
    PermissionAuditSerializer, PermissionSerializer,
    BulkUserGroupAssignmentSerializer, AuthUserSerializer
)
from .permissions import IsStaffUser, IsSuperUser, CanManageGroups, CanManageUsers, CanViewAuditLogs
from .services import GroupService, AuditService, SecurityService, PermissionService

User = get_user_model()


class GroupViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing groups
    Staff users can create, read, update groups
    Only superusers can delete groups
    """
    queryset = Group.objects.all().order_by('name')
    serializer_class = GroupSerializer
    permission_classes = [CanManageGroups]

    def get_queryset(self):
        """Filter groups based on query parameters"""
        queryset = super().get_queryset()

        # Search by name
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(name__icontains=search)

        # Filter by permission
        permission_id = self.request.query_params.get('permission_id')
        if permission_id:
            queryset = queryset.filter(permissions__id=permission_id)

        return queryset

    def perform_create(self, serializer):
        """Create group with audit logging"""
        group = serializer.save()
        
        # Log the action
        security_service = SecurityService()
        AuditService.log_action(
            action='group_created',
            performed_by=self.request.user,
            target_group=group,
            details={
                'permissions': list(group.permissions.values_list('codename', flat=True))
            },
            ip_address=security_service.get_client_ip(self.request)
        )

    def perform_update(self, serializer):
        """Update group with audit logging"""
        old_permissions = set(serializer.instance.permissions.values_list('codename', flat=True))
        group = serializer.save()
        new_permissions = set(group.permissions.values_list('codename', flat=True))

        # Log the action
        security_service = SecurityService()
        AuditService.log_action(
            action='group_updated',
            performed_by=self.request.user,
            target_group=group,
            details={
                'old_permissions': list(old_permissions),
                'new_permissions': list(new_permissions)
            },
            ip_address=security_service.get_client_ip(self.request)
        )

    def perform_destroy(self, instance):
        """Delete group with audit logging (superuser only)"""
        security_service = SecurityService()
        AuditService.log_action(
            action='group_deleted',
            performed_by=self.request.user,
            target_group=instance,
            details={'group_name': instance.name},
            ip_address=security_service.get_client_ip(self.request)
        )
        instance.delete()

    @action(detail=True, methods=['get'], permission_classes=[IsStaffUser])
    def members(self, request, pk=None):
        """Get all members of a group"""
        group = self.get_object()
        group_service = GroupService()
        memberships = group_service.get_group_members(group)
        
        serializer = GroupMembershipSerializer(memberships, many=True)
        return Response({
            'success': True,
            'members': serializer.data
        })

    @action(detail=True, methods=['post'], permission_classes=[CanManageGroups])
    def add_member(self, request, pk=None):
        """Add a user to the group"""
        group = self.get_object()
        serializer = UserGroupAssignmentSerializer(data=request.data)
        
        if serializer.is_valid():
            user_id = serializer.validated_data['user_id']
            notes = serializer.validated_data.get('notes', '')
            
            try:
                user = User.objects.get(id=user_id)
                group_service = GroupService()
                security_service = SecurityService()
                
                membership, created = group_service.add_user_to_group(
                    user=user,
                    group=group,
                    assigned_by=request.user,
                    notes=notes,
                    ip_address=security_service.get_client_ip(request)
                )
                
                if created:
                    membership_serializer = GroupMembershipSerializer(membership)
                    return Response({
                        'success': True,
                        'message': f'User {user.email} added to group {group.name}',
                        'membership': membership_serializer.data
                    })
                else:
                    return Response({
                        'success': False,
                        'error': 'User is already a member of this group'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'User not found'
                }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[CanManageGroups])
    def remove_member(self, request, pk=None):
        """Remove a user from the group"""
        group = self.get_object()
        user_id = request.data.get('user_id')
        
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(id=user_id)
            group_service = GroupService()
            security_service = SecurityService()
            
            removed = group_service.remove_user_from_group(
                user=user,
                group=group,
                removed_by=request.user,
                ip_address=security_service.get_client_ip(request)
            )
            
            if removed:
                return Response({
                    'success': True,
                    'message': f'User {user.email} removed from group {group.name}'
                })
            else:
                return Response({
                    'success': False,
                    'error': 'User is not a member of this group'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except User.DoesNotExist:
            return Response({
                'success': False,
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'], permission_classes=[CanManageGroups])
    def bulk_assign(self, request, pk=None):
        """Assign multiple users to the group"""
        group = self.get_object()
        serializer = BulkUserGroupAssignmentSerializer(data=request.data)
        
        if serializer.is_valid():
            user_ids = serializer.validated_data['user_ids']
            notes = serializer.validated_data.get('notes', '')
            
            group_service = GroupService()
            security_service = SecurityService()
            
            results = group_service.bulk_assign_users(
                user_ids=user_ids,
                group=group,
                assigned_by=request.user,
                notes=notes,
                ip_address=security_service.get_client_ip(request)
            )
            
            success_count = sum(1 for r in results if r['created'])
            
            return Response({
                'success': True,
                'message': f'{success_count} users assigned to group {group.name}',
                'results': [
                    {
                        'user_email': r['user'].email,
                        'assigned': r['created']
                    } for r in results
                ]
            })
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing and managing users
    Staff users can view user information and manage group assignments
    Only superusers can toggle staff status
    """
    queryset = User.objects.filter(is_staff=True).order_by('email')
    permission_classes = [CanManageUsers]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'retrieve':
            return UserDetailSerializer
        return UserBasicSerializer

    def get_queryset(self):
        """Filter users based on query parameters"""
        queryset = super().get_queryset()

        # Search by email
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(email__icontains=search)

        # Filter by group
        group_id = self.request.query_params.get('group_id')
        if group_id:
            queryset = queryset.filter(groups__id=group_id)

        # Filter by staff status
        is_staff = self.request.query_params.get('is_staff')
        if is_staff is not None:
            queryset = queryset.filter(is_staff=is_staff.lower() == 'true')

        return queryset

    @action(detail=True, methods=['get'], permission_classes=[CanManageUsers])
    def groups(self, request, pk=None):
        """Get all groups for a user"""
        user = self.get_object()
        memberships = GroupMembership.objects.filter(
            user=user,
            is_active=True
        ).select_related('group', 'assigned_by')

        serializer = GroupMembershipSerializer(memberships, many=True)
        return Response({
            'success': True,
            'groups': serializer.data
        })

    @action(detail=True, methods=['post'], permission_classes=[IsSuperUser])
    def toggle_staff(self, request, pk=None):
        """Toggle staff status for a user (superuser only)"""
        user = self.get_object()

        if user.is_superuser:
            return Response({
                'success': False,
                'error': 'Cannot modify superuser staff status'
            }, status=status.HTTP_400_BAD_REQUEST)

        old_status = user.is_staff
        user.is_staff = not user.is_staff
        user.save()

        # Log the action
        security_service = SecurityService()
        AuditService.log_action(
            action='user_staff_toggled',
            performed_by=request.user,
            target_user=user,
            details={
                'old_status': old_status,
                'new_status': user.is_staff
            },
            ip_address=security_service.get_client_ip(request)
        )

        return Response({
            'success': True,
            'message': f'User {user.email} staff status set to {user.is_staff}',
            'is_staff': user.is_staff
        })


class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing available permissions
    Staff users can view all permissions for group assignment
    """
    queryset = Permission.objects.all().order_by('content_type__app_label', 'codename')
    serializer_class = PermissionSerializer
    permission_classes = [IsStaffUser]

    def get_queryset(self):
        """Filter permissions based on query parameters"""
        queryset = super().get_queryset()

        # Filter by app
        app_label = self.request.query_params.get('app')
        if app_label:
            queryset = queryset.filter(content_type__app_label=app_label)

        # Filter by model
        model = self.request.query_params.get('model')
        if model:
            queryset = queryset.filter(content_type__model=model)

        # Search by name or codename
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(codename__icontains=search)
            )

        return queryset

    def list(self, request, *args, **kwargs):
        """List permissions grouped by content type"""
        permission_service = PermissionService()
        grouped_permissions = permission_service.get_grouped_permissions()

        return Response({
            'success': True,
            'total_permissions': self.get_queryset().count(),
            'grouped_permissions': grouped_permissions
        })


class AuditViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs
    Only staff users with audit permissions can access
    """
    queryset = PermissionAudit.objects.all().order_by('-timestamp')
    serializer_class = PermissionAuditSerializer
    permission_classes = [CanViewAuditLogs]

    def get_queryset(self):
        """Filter audit logs based on query parameters"""
        queryset = super().get_queryset()

        # Filter by action
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by user
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(performed_by_id=user_id)

        # Filter by date range
        from_date = self.request.query_params.get('from_date')
        if from_date:
            queryset = queryset.filter(timestamp__gte=from_date)

        to_date = self.request.query_params.get('to_date')
        if to_date:
            queryset = queryset.filter(timestamp__lte=to_date)

        return queryset

    @action(detail=False, methods=['get'], permission_classes=[CanViewAuditLogs])
    def summary(self, request):
        """Get audit summary statistics"""
        now = timezone.now()
        periods = {
            '7_days': now - timedelta(days=7),
            '30_days': now - timedelta(days=30),
            '90_days': now - timedelta(days=90),
        }

        summary = {}

        for period_name, start_date in periods.items():
            period_audits = PermissionAudit.objects.filter(timestamp__gte=start_date)

            # Group by action
            action_stats = period_audits.values('action').annotate(
                count=Count('action')
            ).order_by('-count')

            # Group by user
            user_stats = period_audits.values(
                'performed_by__email'
            ).annotate(
                count=Count('performed_by')
            ).order_by('-count')[:10]

            summary[period_name] = {
                'total_actions': period_audits.count(),
                'action_breakdown': list(action_stats),
                'top_users': list(user_stats)
            }

        return Response({
            'success': True,
            'summary': summary,
            'generated_at': now
        })
