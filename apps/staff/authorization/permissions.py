from rest_framework.permissions import BasePermission
from django.contrib.auth.models import Group


class IsStaffUser(BasePermission):
    """
    Permission class to check if the user is staff
    Used for general staff API access
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_staff
        )


class IsSuperUser(BasePermission):
    """
    Permission class to check if the user is a superuser
    Used for system administration endpoints
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_superuser
        )


class CanManageGroups(BasePermission):
    """
    Permission class to check if the user can manage groups
    Staff users can create/read/update groups
    Only superusers can delete groups
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # For DELETE operations, require superuser
        if request.method == 'DELETE':
            return request.user.is_superuser

        return True


class CanManageUsers(BasePermission):
    """
    Permission class to check if the user can manage other users
    Staff users can view and manage user-group assignments
    Only superusers can toggle staff status
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # For staff status toggle, require superuser
        if hasattr(view, 'action') and view.action == 'toggle_staff':
            return request.user.is_superuser

        return True


class CanViewAuditLogs(BasePermission):
    """
    Permission class to check if the user can view audit logs
    Only staff users with specific permissions can view audit logs
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superusers can always view audit logs
        if request.user.is_superuser:
            return True

        # Check if user has audit viewing permission
        # This can be customized based on your specific requirements
        return request.user.has_perm('staff.view_permissionaudit')


class CanAccessStaffAPI(BasePermission):
    """
    Base permission for all staff API endpoints
    Ensures user is authenticated, active, and has staff status
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff
        )

    def has_object_permission(self, request, view, obj):
        """
        Object-level permission check
        Can be overridden by specific views for fine-grained control
        """
        return self.has_permission(request, view)
