from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import GroupViewSet, UserViewSet, PermissionViewSet, AuditViewSet
from .auth_views import CurrentUserView, UserPermissionsView, CheckPermissionView

router = DefaultRouter()
router.register(r'groups', GroupViewSet, basename='auth-groups')
router.register(r'users', UserViewSet, basename='auth-users')
router.register(r'permissions', PermissionViewSet, basename='auth-permissions')
router.register(r'audit', AuditViewSet, basename='auth-audit')

app_name = 'authorization'

urlpatterns = [
    # Authorization endpoints (authentication handled by core app)
    path('auth/user/', CurrentUserView.as_view(), name='current-user'),
    path('auth/permissions/', UserPermissionsView.as_view(), name='user-permissions'),
    path('auth/check-permission/', CheckPermissionView.as_view(), name='check-permission'),

    # Include router URLs
    path('', include(router.urls)),
]
