"""
Constants for staff app
Centralized constants used across different domains
"""

# Audit Action Types
AUDIT_ACTIONS = {
    'GROUP_CREATED': 'group_created',
    'GROUP_UPDATED': 'group_updated',
    'GROUP_DELETED': 'group_deleted',
    'USER_ADDED_TO_GROUP': 'user_added_to_group',
    'USER_REMOVED_FROM_GROUP': 'user_removed_from_group',
    'PERMISSION_GRANTED': 'permission_granted',
    'PERMISSION_REVOKED': 'permission_revoked',
    'USER_STAFF_TOGGLED': 'user_staff_toggled',
    'PRODUCT_CREATED': 'product_created',
    'PRODUCT_UPDATED': 'product_updated',
    'PRODUCT_DELETED': 'product_deleted',
    'ORDER_STATUS_CHANGED': 'order_status_changed',
    'ORDER_ASSIGNED': 'order_assigned',
}

# Staff Group Names (predefined groups)
STAFF_GROUPS = {
    'SUPER_ADMIN': 'Super Administrator (SA)',
    'PRODUCT_MANAGER': 'Product Management Executive (PME)',
    'INVENTORY_MANAGER': 'Inventory Management Executive (IME)',
    'ORDER_MANAGER': 'Order Management Executive (OME)',
    'CUSTOMER_SERVICE': 'Customer Service Representative (CSR)',
    'CUSTOMER_ANALYST': 'Customer Data Analyst (CDA)',
    'CONTENT_MANAGER': 'Content Management Executive (CTME)',
    'CONTENT_MODERATOR': 'Content Moderator (CTM)',
    'FINANCE_MANAGER': 'Finance Manager (FM)',
    'BUSINESS_ANALYST': 'Business Analyst (BA)',
}

# Permission Categories
PERMISSION_CATEGORIES = {
    'PRODUCTS': 'products',
    'ORDERS': 'orders',
    'CUSTOMERS': 'customers',
    'INVENTORY': 'inventory',
    'CONTENT': 'content',
    'FINANCE': 'finance',
    'ANALYTICS': 'analytics',
    'SYSTEM': 'system',
}

# Cache Keys
CACHE_KEYS = {
    'USER_PERMISSIONS': 'staff_user_permissions_{user_id}',
    'USER_GROUPS': 'staff_user_groups_{user_id}',
    'USER_PROFILE': 'staff_user_profile_{user_id}',
    'GROUP_MEMBERS': 'staff_group_members_{group_id}',
    'PERMISSION_LIST': 'staff_permissions_list',
}

# Cache Timeouts (in seconds)
CACHE_TIMEOUTS = {
    'USER_PERMISSIONS': 300,  # 5 minutes
    'USER_GROUPS': 300,       # 5 minutes
    'USER_PROFILE': 600,      # 10 minutes
    'GROUP_MEMBERS': 180,     # 3 minutes
    'PERMISSION_LIST': 3600,  # 1 hour
}

# API Response Messages
API_MESSAGES = {
    'SUCCESS': 'Operation completed successfully',
    'CREATED': 'Resource created successfully',
    'UPDATED': 'Resource updated successfully',
    'DELETED': 'Resource deleted successfully',
    'NOT_FOUND': 'Resource not found',
    'PERMISSION_DENIED': 'You do not have permission to perform this action',
    'INVALID_DATA': 'Invalid data provided',
    'USER_NOT_STAFF': 'User must be a staff member',
    'USER_ALREADY_MEMBER': 'User is already a member of this group',
    'USER_NOT_MEMBER': 'User is not a member of this group',
    'CANNOT_DELETE_SUPERUSER': 'Cannot modify superuser permissions',
}

# Pagination Defaults
PAGINATION = {
    'DEFAULT_PAGE_SIZE': 20,
    'MAX_PAGE_SIZE': 100,
    'MIN_PAGE_SIZE': 1,
}

# File Upload Limits
FILE_UPLOAD = {
    'MAX_SIZE_MB': 10,
    'ALLOWED_EXTENSIONS': ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'],
    'IMAGE_EXTENSIONS': ['.jpg', '.jpeg', '.png', '.gif'],
    'DOCUMENT_EXTENSIONS': ['.pdf', '.doc', '.docx'],
}

# Date/Time Formats
DATE_FORMATS = {
    'API_DATE': '%Y-%m-%d',
    'API_DATETIME': '%Y-%m-%dT%H:%M:%S.%fZ',
    'DISPLAY_DATE': '%B %d, %Y',
    'DISPLAY_DATETIME': '%B %d, %Y at %I:%M %p',
}

# Status Choices
STATUS_CHOICES = {
    'ACTIVE': 'active',
    'INACTIVE': 'inactive',
    'PENDING': 'pending',
    'SUSPENDED': 'suspended',
    'ARCHIVED': 'archived',
}

# Priority Levels
PRIORITY_LEVELS = {
    'LOW': 1,
    'MEDIUM': 2,
    'HIGH': 3,
    'URGENT': 4,
    'CRITICAL': 5,
}

# Log Levels
LOG_LEVELS = {
    'DEBUG': 'debug',
    'INFO': 'info',
    'WARNING': 'warning',
    'ERROR': 'error',
    'CRITICAL': 'critical',
}

# HTTP Status Codes (commonly used)
HTTP_STATUS = {
    'OK': 200,
    'CREATED': 201,
    'NO_CONTENT': 204,
    'BAD_REQUEST': 400,
    'UNAUTHORIZED': 401,
    'FORBIDDEN': 403,
    'NOT_FOUND': 404,
    'METHOD_NOT_ALLOWED': 405,
    'CONFLICT': 409,
    'INTERNAL_SERVER_ERROR': 500,
}

# Validation Rules
VALIDATION = {
    'MIN_PASSWORD_LENGTH': 8,
    'MAX_USERNAME_LENGTH': 150,
    'MAX_EMAIL_LENGTH': 254,
    'MAX_NAME_LENGTH': 100,
    'MAX_DESCRIPTION_LENGTH': 500,
    'MAX_NOTES_LENGTH': 1000,
}

# Feature Flags
FEATURES = {
    'AUDIT_LOGGING': True,
    'CACHE_ENABLED': True,
    'EMAIL_NOTIFICATIONS': True,
    'BULK_OPERATIONS': True,
    'ADVANCED_SEARCH': True,
    'EXPORT_DATA': True,
}

# Email Templates
EMAIL_TEMPLATES = {
    'USER_ADDED_TO_GROUP': 'staff/emails/user_added_to_group.html',
    'USER_REMOVED_FROM_GROUP': 'staff/emails/user_removed_from_group.html',
    'PERMISSION_CHANGED': 'staff/emails/permission_changed.html',
    'ACCOUNT_SUSPENDED': 'staff/emails/account_suspended.html',
}

# Default Permissions by Group
DEFAULT_GROUP_PERMISSIONS = {
    STAFF_GROUPS['PRODUCT_MANAGER']: [
        'products.add_product',
        'products.change_product',
        'products.delete_product',
        'products.view_product',
        'products.add_category',
        'products.change_category',
        'products.view_category',
    ],
    STAFF_GROUPS['ORDER_MANAGER']: [
        'orders.view_order',
        'orders.change_order',
        'orders.add_order',
        'customers.view_customer',
    ],
    STAFF_GROUPS['CUSTOMER_SERVICE']: [
        'customers.view_customer',
        'customers.change_customer',
        'orders.view_order',
        'orders.change_order',
    ],
    STAFF_GROUPS['BUSINESS_ANALYST']: [
        'products.view_product',
        'orders.view_order',
        'customers.view_customer',
    ],
}
