# Import serializers from domain-specific modules
from .authorization.serializers import (
    GroupSerializer, UserBasicSerializer, UserDetailSerializer,
    GroupMembershipSerializer, UserGroupAssignmentSerializer,
    PermissionAuditSerializer, PermissionSerializer,
    BulkUserGroupAssignmentSerializer, AuthUserSerializer
)

# Re-export for backward compatibility
__all__ = [
    'GroupSerializer', 'UserBasicSerializer', 'UserDetailSerializer',
    'GroupMembershipSerializer', 'UserGroupAssignmentSerializer',
    'PermissionAuditSerializer', 'PermissionSerializer',
    'BulkUserGroupAssignmentSerializer', 'AuthUserSerializer'
]

# All RBAC-related serializers have been moved to apps.staff.authorization.serializers
# This file now serves as a compatibility layer for imports
