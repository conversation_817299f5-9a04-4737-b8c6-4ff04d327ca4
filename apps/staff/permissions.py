from rest_framework.permissions import BasePermission
from django.contrib.auth.models import Group


class IsStaffUser(BasePermission):
    """
    Permission class to check if the user is staff
    Used for general staff API access
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_staff
        )


class IsSuperUser(BasePermission):
    """
    Permission class to check if the user is a superuser
    Used for system administration endpoints
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_superuser
        )


class HasGroupPermission(BasePermission):
    """
    Permission class to check if the user belongs to required groups
    Usage: Set required_groups on the view
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superuser has all permissions
        if request.user.is_superuser:
            return True

        # Check if view has required_groups attribute
        required_groups = getattr(view, 'required_groups', [])
        if not required_groups:
            return True  # No specific groups required

        # Check if a user belongs to any of the required groups
        user_groups = request.user.groups.values_list('name', flat=True)
        return any(group in user_groups for group in required_groups)


class HasSpecificPermission(BasePermission):
    """
    Permission class to check if a user has specific Django permissions
    Usage: Set required_permissions on the view
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superuser has all permissions
        if request.user.is_superuser:
            return True

        # Check if view has required_permissions attribute
        required_permissions = getattr(view, 'required_permissions', [])
        if not required_permissions:
            return True  # No specific permissions required

        # Check if the user has any of the required permissions
        return any(request.user.has_perm(perm) for perm in required_permissions)


class CanManageGroups(BasePermission):
    """
    Permission class for group management operations
    Only staff users can manage groups
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_staff
        )


class CanManageUsers(BasePermission):
    """
    Permission class for user management operations
    Only superusers can manage users
    """

    def has_permission(self, request, view):
        return (
                request.user and
                request.user.is_authenticated and
                request.user.is_superuser
        )


class HasBusinessPermission(BasePermission):
    """
    Permission class for business operations
    Checks both group membership and specific permissions
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False

        # Superuser has all permissions
        if request.user.is_superuser:
            return True

        # Check group membership
        required_groups = getattr(view, 'required_groups', [])
        if required_groups:
            user_groups = request.user.groups.values_list('name', flat=True)
            if not any(group in user_groups for group in required_groups):
                return False

        # Check specific permissions
        required_permissions = getattr(view, 'required_permissions', [])
        if required_permissions:
            if not any(request.user.has_perm(perm) for perm in required_permissions):
                return False

        return True
