# Import permissions from domain-specific modules
from .authorization.permissions import (
    IsStaffUser, IsSuperUser, CanManageGroups, CanManageUsers,
    CanViewAuditLogs, CanAccessStaffAPI
)

# Re-export for backward compatibility
__all__ = [
    'IsStaffUser', 'IsSuperUser', 'CanManageGroups', 'CanManageUsers',
    'CanViewAuditLogs', 'CanAccessStaffAPI'
]

# All RBAC-related permissions have been moved to apps.staff.authorization.permissions
# This file now serves as a compatibility layer for imports
