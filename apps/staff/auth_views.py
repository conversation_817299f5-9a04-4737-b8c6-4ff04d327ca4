# Import auth views from domain-specific modules
from .authorization.auth_views import (
    CurrentUserView, UserPermissionsView, CheckPermissionView,
    current_user, user_permissions, check_permission
)

# Re-export for backward compatibility
__all__ = [
    'CurrentUserView', 'UserPermissionsView', 'CheckPermissionView',
    'current_user', 'user_permissions', 'check_permission'
]

# All RBAC-related auth views have been moved to apps.staff.authorization.auth_views
# This file now serves as a compatibility layer for imports
