from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import SecurityService

User = get_user_model()


# Note: Core app handles authentication (login/logout)
# This file only contains staff-specific authorization views

class CurrentUserView(APIView):
    """
    Get current authenticated staff user information with groups and permissions
    This view provides staff-specific user data including;
    - User basic information
    - Group memberships
    - Permissions (both direct and inherited from groups)
    Note: The core app handles authentication, this only provides staff-specific data.
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get current staff user information

        Returns:
            Response: JSON containing user data with groups and permissions
        """
        serializer = AuthUserSerializer(request.user)
        return Response({
            'success': True,
            'user': serializer.data
        })


class UserPermissionsView(APIView):
    """
    Get current user's permissions and groups breakdown
    This view provides detailed permission information including;
    - List of user's groups
    - All permissions (direct and inherited)
    - Group-specific permission breakdown
    - Superuser status
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get detailed permissions and groups for current user
        Returns:
            Response: JSON containing detailed permission breakdown
        """
        user = request.user

        # Get user groups
        groups = list(user.groups.values_list('name', flat=True))

        # Get all permissions
        permissions = list(user.get_all_permissions())

        # Get group permissions breakdown
        group_permissions = {}
        for group in user.groups.all():
            group_permissions[group.name] = list(
                group.permissions.values_list('codename', flat=True)
            )

        return Response({
            'success': True,
            'user_id': user.id,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'groups': groups,
            'permissions': permissions,
            'group_permissions': group_permissions
        })


class CheckPermissionView(APIView):
    """
    Check if the current user has specific permission.
    This view allows frontend applications to check if the current
    staff user has a specific permission before showing UI elements
    or allowing certain actions.
    """
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Check if the user has specific permission
        Args:
            request: HTTP request containing 'permission' in data
        Returns:
            Response: JSON indicating whether the user has the permission
        """
        permission = request.data.get('permission')

        if not permission:
            return Response({
                'success': False,
                'error': 'Permission parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        has_permission = request.user.has_perm(permission)

        return Response({
            'success': True,
            'permission': permission,
            'has_permission': has_permission
        })


# For backward compatibility, create instances
current_user = CurrentUserView.as_view()
user_permissions = UserPermissionsView.as_view()
check_permission = CheckPermissionView.as_view()
