# API Full List - Staff RBAC System

## **Staff Authorization Endpoints**

### **1. Get Staff User Info**
**Available requests:** [GET]

**GET:** `/api/staff/auth/user/`

**Request Example:**
```http
GET /api/staff/auth/user/
Authorization: Bearer {access_token}
```

---

### **2. Check User Permissions**
**Available requests:** [GET]

**GET:** `/api/staff/auth/permissions/`

**Request Example:**
```http
GET /api/staff/auth/permissions/
Authorization: Bearer {access_token}
```

---

### **3. Check Specific Permission**
**Available requests:** [POST]

**POST:** `/api/staff/auth/check-permission/`

**Request Example:**
```http
POST /api/staff/auth/check-permission/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "permission": "products.add_product"
}
```

---

## **Group Management Endpoints**

### **4. List All Groups**
**Available requests:** [GET, POST]

**GET:** `/api/staff/groups/`

**Request Example:**
```http
GET /api/staff/groups/
Authorization: Bearer {access_token}
```

**POST:** `/api/staff/groups/`

**Request Example:**
```http
POST /api/staff/groups/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "New Staff Group",
    "permission_ids": [25, 26, 27, 28]
}
```

---

### **5. Group Detail Operations**
**Available requests:** [GET, PUT, PATCH, DELETE]

**GET:** `/api/staff/groups/{group_id}/`

**Request Example:**
```http
GET /api/staff/groups/1/
Authorization: Bearer {access_token}
```

**PUT:** `/api/staff/groups/{group_id}/`

**Request Example:**
```http
PUT /api/staff/groups/1/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Updated Group Name",
    "permission_ids": [25, 26, 27]
}
```

**PATCH:** `/api/staff/groups/{group_id}/`

**Request Example:**
```http
PATCH /api/staff/groups/1/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Partially Updated Group Name"
}
```

**DELETE:** `/api/staff/groups/{group_id}/`

**Request Example:**
```http
DELETE /api/staff/groups/1/
Authorization: Bearer {access_token}
```

---

### **6. Get Group Members**
**Available requests:** [GET]

**GET:** `/api/staff/groups/{group_id}/members/`

**Request Example:**
```http
GET /api/staff/groups/1/members/
Authorization: Bearer {access_token}
```

---

### **7. Add User to Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/add_member/`

**Request Example:**
```http
POST /api/staff/groups/1/add_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5,
    "notes": "Adding user to product management team"
}
```

---

### **8. Remove User from Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/remove_member/`

**Request Example:**
```http
POST /api/staff/groups/1/remove_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5
}
```

---

### **9. Bulk User Assignment to Group**
**Available requests:** [POST]

**POST:** `/api/staff/groups/{group_id}/bulk_assign/`

**Request Example:**
```http
POST /api/staff/groups/1/bulk_assign/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_ids": [5, 6, 7],
    "notes": "Bulk assignment to product management team"
}
```

---
